// Database types based on the lsp_jwp MySQL schema

export interface Category {
  id: number;
  name: string;
  created_at: string;
}

export interface Book {
  id: number;
  category_id: number;
  title: string;
  author: string;
  description: string;
  stock: number;
  price_per_day: number;
  image: string | null;
  created_at: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'user';
  created_at: string;
}

export interface Order {
  id: number;
  user_id: number;
  status: 'requested' | 'approved' | 'rejected';
  order_date: string;
  return_date: string;
  total_price: number;
  created_at: string;
}

export interface OrderItem {
  id: number;
  order_id: number;
  book_id: number;
  qty_days: number;
  subtotal: number;
}

export interface ContactInfo {
  id: number;
  address: string | null;
  phone: string | null;
  email: string | null;
  map_embed: string | null;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Extended types with relations
export interface BookWithCategory extends Book {
  category_name?: string;
}

export interface OrderWithDetails extends Order {
  user_name?: string;
  user_email?: string;
  items?: OrderItemWithBook[];
}

export interface OrderItemWithBook extends OrderItem {
  book_title?: string;
  book_author?: string;
}

// Form types for creating/updating records
export type CreateCategory = Omit<Category, 'id' | 'created_at'>;
export type UpdateCategory = Partial<CreateCategory>;

export type CreateBook = Omit<Book, 'id' | 'created_at'>;
export type UpdateBook = Partial<CreateBook>;

export type CreateUser = Omit<User, 'id' | 'created_at'>;
export type UpdateUser = Partial<CreateUser>;

export type CreateOrder = Omit<Order, 'id' | 'created_at'>;
export type UpdateOrder = Partial<CreateOrder>;

export type CreateOrderItem = Omit<OrderItem, 'id'>;
export type UpdateOrderItem = Partial<CreateOrderItem>;

export type UpdateContactInfo = Partial<Omit<ContactInfo, 'id'>>;
