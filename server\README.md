# LSP JWP API Server

This is the backend API server for the LSP JWP Book Rental Management System.

## Setup Instructions

### 1. Install Dependencies

```bash
cd server
npm install
```

### 2. Database Setup

1. Make sure MySQL is installed and running on your system
2. Import the database schema from `../database/lsp_jwp.sql`:

```bash
mysql -u root -p < ../database/lsp_jwp.sql
```

Or using phpMyAdmin:
- Open phpMyAdmin
- Create a new database named `lsp_jwp`
- Import the `lsp_jwp.sql` file

### 3. Environment Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` file with your database credentials:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=lsp_jwp
DB_PORT=3306

PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:8080
```

### 4. Start the Server

For development (with auto-restart):
```bash
npm run dev
```

For production:
```bash
npm start
```

The server will start on `http://localhost:3001`

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get category by ID
- `POST /api/categories` - Create new category
- `PUT /api/categories/:id` - Update category
- `DELETE /api/categories/:id` - Delete category

## Testing the API

You can test the API using curl or any API testing tool:

```bash
# Get all categories
curl http://localhost:3001/api/categories

# Create a new category
curl -X POST http://localhost:3001/api/categories \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Category"}'
```

## Troubleshooting

### Database Connection Issues
1. Make sure MySQL is running
2. Check your database credentials in `.env`
3. Ensure the `lsp_jwp` database exists
4. Check if the user has proper permissions

### Port Already in Use
If port 3001 is already in use, change the `PORT` in your `.env` file and update the `API_BASE_URL` in the frontend configuration.

### CORS Issues
Make sure the `FRONTEND_URL` in `.env` matches your frontend development server URL.
