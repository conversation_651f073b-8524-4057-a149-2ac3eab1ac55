# LSP JWP Setup Guide

## Quick Setup Steps

### 1. Database Setup
1. Install MySQL if not already installed
2. Start MySQL service
3. Create the database:
   ```sql
   CREATE DATABASE lsp_jwp;
   ```
4. Import the schema:
   ```bash
   mysql -u root -p lsp_jwp < database/lsp_jwp.sql
   ```

### 2. Backend Setup
```bash
cd server
npm install
cp .env.example .env
```

Edit `server/.env`:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=lsp_jwp
DB_PORT=3306
PORT=3001
FRONTEND_URL=http://localhost:8080
```

Start the backend:
```bash
npm run dev
```

### 3. Frontend Setup
```bash
# From project root
npm install
cp .env.example .env
```

Edit `.env`:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=lsp_jwp
DB_PORT=3306
API_BASE_URL=http://localhost:3001/api
```

Start the frontend:
```bash
npm run dev
```

### 4. Access the Application
- Frontend: http://localhost:8080
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/health

## Troubleshooting

### Database Connection Issues
- Ensure MySQL is running
- Check credentials in `.env` files
- Verify database `lsp_jwp` exists
- Check user permissions

### Port Conflicts
- Change ports in `.env` files if needed
- Update corresponding URLs

### API Connection Issues
- Ensure backend server is running
- Check CORS settings
- Verify API_BASE_URL in frontend config

## Development Workflow

1. Start MySQL service
2. Start backend server: `cd server && npm run dev`
3. Start frontend server: `npm run dev`
4. Access application at http://localhost:8080

## Production Deployment

1. Build frontend: `npm run build`
2. Start backend: `cd server && npm start`
3. Serve frontend build files with a web server
