{"name": "lsp-jwp-api", "version": "1.0.0", "description": "API server for LSP JWP Book Rental System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["api", "mysql", "book-rental"], "author": "LSP JWP", "license": "MIT"}