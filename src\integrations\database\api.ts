import axios from 'axios';
import { apiConfig } from './config';
import type {
  Category, Book, User, Order, OrderItem, ContactInfo,
  CreateCategory, UpdateCategory,
  CreateBook, UpdateBook,
  CreateUser, UpdateUser,
  CreateOrder, UpdateOrder,
  CreateOrderItem, UpdateOrderItem,
  UpdateContactInfo,
  ApiResponse,
  BookWithCategory,
  OrderWithDetails
} from './types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Categories API
export const categoriesApi = {
  getAll: async (): Promise<Category[]> => {
    const response = await api.get<ApiResponse<Category[]>>('/categories');
    return response.data.data || [];
  },

  getById: async (id: number): Promise<Category | null> => {
    const response = await api.get<ApiResponse<Category>>(`/categories/${id}`);
    return response.data.data || null;
  },

  create: async (category: CreateCategory): Promise<Category> => {
    const response = await api.post<ApiResponse<Category>>('/categories', category);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to create category');
    }
    return response.data.data;
  },

  update: async (id: number, updates: UpdateCategory): Promise<Category> => {
    const response = await api.put<ApiResponse<Category>>(`/categories/${id}`, updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to update category');
    }
    return response.data.data;
  },

  delete: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/categories/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete category');
    }
  },
};

// Books API
export const booksApi = {
  getAll: async (): Promise<BookWithCategory[]> => {
    const response = await api.get<ApiResponse<BookWithCategory[]>>('/books');
    return response.data.data || [];
  },

  getById: async (id: number): Promise<BookWithCategory | null> => {
    const response = await api.get<ApiResponse<BookWithCategory>>(`/books/${id}`);
    return response.data.data || null;
  },

  create: async (book: CreateBook): Promise<Book> => {
    const response = await api.post<ApiResponse<Book>>('/books', book);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to create book');
    }
    return response.data.data;
  },

  update: async (id: number, updates: UpdateBook): Promise<Book> => {
    const response = await api.put<ApiResponse<Book>>(`/books/${id}`, updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to update book');
    }
    return response.data.data;
  },

  delete: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/books/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete book');
    }
  },
};

// Users API
export const usersApi = {
  getAll: async (): Promise<User[]> => {
    const response = await api.get<ApiResponse<User[]>>('/users');
    return response.data.data || [];
  },

  getById: async (id: number): Promise<User | null> => {
    const response = await api.get<ApiResponse<User>>(`/users/${id}`);
    return response.data.data || null;
  },

  create: async (user: CreateUser): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/users', user);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to create user');
    }
    return response.data.data;
  },

  update: async (id: number, updates: UpdateUser): Promise<User> => {
    const response = await api.put<ApiResponse<User>>(`/users/${id}`, updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to update user');
    }
    return response.data.data;
  },

  delete: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/users/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete user');
    }
  },
};

// Orders API
export const ordersApi = {
  getAll: async (): Promise<OrderWithDetails[]> => {
    const response = await api.get<ApiResponse<OrderWithDetails[]>>('/orders');
    return response.data.data || [];
  },

  getById: async (id: number): Promise<OrderWithDetails | null> => {
    const response = await api.get<ApiResponse<OrderWithDetails>>(`/orders/${id}`);
    return response.data.data || null;
  },

  create: async (order: CreateOrder): Promise<Order> => {
    const response = await api.post<ApiResponse<Order>>('/orders', order);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to create order');
    }
    return response.data.data;
  },

  update: async (id: number, updates: UpdateOrder): Promise<Order> => {
    const response = await api.put<ApiResponse<Order>>(`/orders/${id}`, updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to update order');
    }
    return response.data.data;
  },

  delete: async (id: number): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/orders/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete order');
    }
  },
};

// Contact Info API
export const contactApi = {
  get: async (): Promise<ContactInfo | null> => {
    const response = await api.get<ApiResponse<ContactInfo>>('/contact');
    return response.data.data || null;
  },

  update: async (updates: UpdateContactInfo): Promise<ContactInfo> => {
    const response = await api.put<ApiResponse<ContactInfo>>('/contact', updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to update contact info');
    }
    return response.data.data;
  },
};

export default api;
