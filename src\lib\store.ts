
import { Category, Book, User, Order } from '@/types';

// Mock data for testing
export const mockCategories: Category[] = [
  { id: 1, name: 'Fiction', created_at: '2024-01-01T00:00:00Z' },
  { id: 2, name: 'Non-Fiction', created_at: '2024-01-02T00:00:00Z' },
  { id: 3, name: 'Science', created_at: '2024-01-03T00:00:00Z' },
  { id: 4, name: 'Biography', created_at: '2024-01-04T00:00:00Z' },
  { id: 5, name: 'Romance', created_at: '2024-01-05T00:00:00Z' },
];

export const mockBooks: Book[] = [
  {
    id: 1,
    category_id: 1,
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    description: 'A classic American novel set in the Jazz Age',
    stock: 5,
    price_per_day: 15000,
    image: '/placeholder.svg',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    category_id: 2,
    title: 'Sapiens',
    author: '<PERSON><PERSON>',
    description: 'A brief history of humankind',
    stock: 3,
    price_per_day: 20000,
    image: '/placeholder.svg',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    category_id: 3,
    title: 'A Brief History of Time',
    author: 'Stephen Hawking',
    description: 'From the Big Bang to Black Holes',
    stock: 2,
    price_per_day: 18000,
    image: '/placeholder.svg',
    created_at: '2024-01-03T00:00:00Z'
  },
];

export const mockUsers: User[] = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'user',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'user',
    created_at: '2024-01-03T00:00:00Z'
  },
];

export const mockOrders: Order[] = [
  {
    id: 1,
    user_id: 2,
    status: 'approved',
    order_date: '2024-01-10',
    return_date: '2024-01-17',
    total_price: 105000,
    created_at: '2024-01-10T00:00:00Z'
  },
  {
    id: 2,
    user_id: 3,
    status: 'requested',
    order_date: '2024-01-12',
    return_date: '2024-01-19',
    total_price: 140000,
    created_at: '2024-01-12T00:00:00Z'
  },
];

// Simple in-memory store (in real app, this would be API calls)
let categories = [...mockCategories];
let books = [...mockBooks];
let users = [...mockUsers];
let orders = [...mockOrders];

export const dataStore = {
  // Categories CRUD
  getCategories: () => categories,
  getCategoryById: (id: number) => categories.find(c => c.id === id),
  addCategory: (category: Omit<Category, 'id' | 'created_at'>) => {
    const newCategory: Category = {
      id: Math.max(...categories.map(c => c.id)) + 1,
      ...category,
      created_at: new Date().toISOString()
    };
    categories.push(newCategory);
    return newCategory;
  },
  updateCategory: (id: number, updates: Partial<Category>) => {
    const index = categories.findIndex(c => c.id === id);
    if (index !== -1) {
      categories[index] = { ...categories[index], ...updates };
      return categories[index];
    }
    return null;
  },
  deleteCategory: (id: number) => {
    categories = categories.filter(c => c.id !== id);
  },

  // Books CRUD
  getBooks: () => books,
  getBookById: (id: number) => books.find(b => b.id === id),
  addBook: (book: Omit<Book, 'id' | 'created_at'>) => {
    const newBook: Book = {
      id: Math.max(...books.map(b => b.id)) + 1,
      ...book,
      created_at: new Date().toISOString()
    };
    books.push(newBook);
    return newBook;
  },
  updateBook: (id: number, updates: Partial<Book>) => {
    const index = books.findIndex(b => b.id === id);
    if (index !== -1) {
      books[index] = { ...books[index], ...updates };
      return books[index];
    }
    return null;
  },
  deleteBook: (id: number) => {
    books = books.filter(b => b.id !== id);
  },

  // Users CRUD
  getUsers: () => users,
  getUserById: (id: number) => users.find(u => u.id === id),
  addUser: (user: Omit<User, 'id' | 'created_at'>) => {
    const newUser: User = {
      id: Math.max(...users.map(u => u.id)) + 1,
      ...user,
      created_at: new Date().toISOString()
    };
    users.push(newUser);
    return newUser;
  },
  updateUser: (id: number, updates: Partial<User>) => {
    const index = users.findIndex(u => u.id === id);
    if (index !== -1) {
      users[index] = { ...users[index], ...updates };
      return users[index];
    }
    return null;
  },
  deleteUser: (id: number) => {
    users = users.filter(u => u.id !== id);
  },

  // Orders CRUD
  getOrders: () => orders,
  getOrderById: (id: number) => orders.find(o => o.id === id),
  addOrder: (order: Omit<Order, 'id' | 'created_at'>) => {
    const newOrder: Order = {
      id: Math.max(...orders.map(o => o.id)) + 1,
      ...order,
      created_at: new Date().toISOString()
    };
    orders.push(newOrder);
    return newOrder;
  },
  updateOrder: (id: number, updates: Partial<Order>) => {
    const index = orders.findIndex(o => o.id === id);
    if (index !== -1) {
      orders[index] = { ...orders[index], ...updates };
      return orders[index];
    }
    return null;
  },
  deleteOrder: (id: number) => {
    orders = orders.filter(o => o.id !== id);
  },
};
