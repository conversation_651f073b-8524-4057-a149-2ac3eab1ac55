
import {
  categoriesApi,
  booksApi,
  usersApi,
  ordersApi,
  contactApi
} from '@/integrations/database/api';
import type {
  Category,
  Book,
  User,
  Order,
  ContactInfo,
  CreateCategory,
  UpdateCategory,
  CreateBook,
  UpdateBook,
  CreateUser,
  UpdateUser,
  CreateOrder,
  UpdateOrder,
  UpdateContactInfo,
  BookWithCategory,
  OrderWithDetails
} from '@/types';

// Mock data for fallback when API is not available
export const mockCategories: Category[] = [
  { id: 1, name: 'Fiction', created_at: '2024-01-01T00:00:00Z' },
  { id: 2, name: 'Non-Fiction', created_at: '2024-01-02T00:00:00Z' },
  { id: 3, name: 'Science', created_at: '2024-01-03T00:00:00Z' },
  { id: 4, name: 'Biography', created_at: '2024-01-04T00:00:00Z' },
  { id: 5, name: 'Romance', created_at: '2024-01-05T00:00:00Z' },
];

export const mockBooks: Book[] = [
  {
    id: 1,
    category_id: 1,
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    description: 'A classic American novel set in the Jazz Age',
    stock: 5,
    price_per_day: 15000,
    image: '/placeholder.svg',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    category_id: 2,
    title: 'Sapiens',
    author: 'Yuval Noah Harari',
    description: 'A brief history of humankind',
    stock: 3,
    price_per_day: 20000,
    image: '/placeholder.svg',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    category_id: 3,
    title: 'A Brief History of Time',
    author: 'Stephen Hawking',
    description: 'From the Big Bang to Black Holes',
    stock: 2,
    price_per_day: 18000,
    image: '/placeholder.svg',
    created_at: '2024-01-03T00:00:00Z'
  },
];

export const mockUsers: User[] = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'user',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: 'user',
    created_at: '2024-01-03T00:00:00Z'
  },
];

export const mockOrders: Order[] = [
  {
    id: 1,
    user_id: 2,
    status: 'approved',
    order_date: '2024-01-10',
    return_date: '2024-01-17',
    total_price: 105000,
    created_at: '2024-01-10T00:00:00Z'
  },
  {
    id: 2,
    user_id: 3,
    status: 'requested',
    order_date: '2024-01-12',
    return_date: '2024-01-19',
    total_price: 140000,
    created_at: '2024-01-12T00:00:00Z'
  },
];

// Fallback to mock data when API is not available
let categories = [...mockCategories];
let books = [...mockBooks];
let users = [...mockUsers];
let orders = [...mockOrders];

export const dataStore = {
  // Categories CRUD - with API integration and fallback
  getCategories: async (): Promise<Category[]> => {
    try {
      return await categoriesApi.getAll();
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return categories;
    }
  },

  getCategoryById: async (id: number): Promise<Category | null> => {
    try {
      return await categoriesApi.getById(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return categories.find(c => c.id === id) || null;
    }
  },

  addCategory: async (category: CreateCategory): Promise<Category> => {
    try {
      return await categoriesApi.create(category);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const newCategory: Category = {
        id: Math.max(...categories.map(c => c.id)) + 1,
        ...category,
        created_at: new Date().toISOString()
      };
      categories.push(newCategory);
      return newCategory;
    }
  },

  updateCategory: async (id: number, updates: UpdateCategory): Promise<Category | null> => {
    try {
      return await categoriesApi.update(id, updates);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const index = categories.findIndex(c => c.id === id);
      if (index !== -1) {
        categories[index] = { ...categories[index], ...updates };
        return categories[index];
      }
      return null;
    }
  },

  deleteCategory: async (id: number): Promise<void> => {
    try {
      await categoriesApi.delete(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      categories = categories.filter(c => c.id !== id);
    }
  },

  // Books CRUD - with API integration and fallback
  getBooks: async (): Promise<BookWithCategory[]> => {
    try {
      return await booksApi.getAll();
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return books.map(book => ({
        ...book,
        category_name: categories.find(c => c.id === book.category_id)?.name
      }));
    }
  },

  getBookById: async (id: number): Promise<BookWithCategory | null> => {
    try {
      return await booksApi.getById(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const book = books.find(b => b.id === id);
      if (book) {
        return {
          ...book,
          category_name: categories.find(c => c.id === book.category_id)?.name
        };
      }
      return null;
    }
  },

  addBook: async (book: CreateBook): Promise<Book> => {
    try {
      return await booksApi.create(book);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const newBook: Book = {
        id: Math.max(...books.map(b => b.id)) + 1,
        ...book,
        created_at: new Date().toISOString()
      };
      books.push(newBook);
      return newBook;
    }
  },

  updateBook: async (id: number, updates: UpdateBook): Promise<Book | null> => {
    try {
      return await booksApi.update(id, updates);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const index = books.findIndex(b => b.id === id);
      if (index !== -1) {
        books[index] = { ...books[index], ...updates };
        return books[index];
      }
      return null;
    }
  },

  deleteBook: async (id: number): Promise<void> => {
    try {
      await booksApi.delete(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      books = books.filter(b => b.id !== id);
    }
  },

  // Users CRUD - with API integration and fallback
  getUsers: async (): Promise<User[]> => {
    try {
      return await usersApi.getAll();
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return users;
    }
  },

  getUserById: async (id: number): Promise<User | null> => {
    try {
      return await usersApi.getById(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return users.find(u => u.id === id) || null;
    }
  },

  addUser: async (user: CreateUser): Promise<User> => {
    try {
      return await usersApi.create(user);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const newUser: User = {
        id: Math.max(...users.map(u => u.id)) + 1,
        ...user,
        created_at: new Date().toISOString()
      };
      users.push(newUser);
      return newUser;
    }
  },

  updateUser: async (id: number, updates: UpdateUser): Promise<User | null> => {
    try {
      return await usersApi.update(id, updates);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const index = users.findIndex(u => u.id === id);
      if (index !== -1) {
        users[index] = { ...users[index], ...updates };
        return users[index];
      }
      return null;
    }
  },

  deleteUser: async (id: number): Promise<void> => {
    try {
      await usersApi.delete(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      users = users.filter(u => u.id !== id);
    }
  },

  // Orders CRUD - with API integration and fallback
  getOrders: async (): Promise<OrderWithDetails[]> => {
    try {
      return await ordersApi.getAll();
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      return orders.map(order => ({
        ...order,
        user_name: users.find(u => u.id === order.user_id)?.name,
        user_email: users.find(u => u.id === order.user_id)?.email
      }));
    }
  },

  getOrderById: async (id: number): Promise<OrderWithDetails | null> => {
    try {
      return await ordersApi.getById(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const order = orders.find(o => o.id === id);
      if (order) {
        return {
          ...order,
          user_name: users.find(u => u.id === order.user_id)?.name,
          user_email: users.find(u => u.id === order.user_id)?.email
        };
      }
      return null;
    }
  },

  addOrder: async (order: CreateOrder): Promise<Order> => {
    try {
      return await ordersApi.create(order);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const newOrder: Order = {
        id: Math.max(...orders.map(o => o.id)) + 1,
        ...order,
        created_at: new Date().toISOString()
      };
      orders.push(newOrder);
      return newOrder;
    }
  },

  updateOrder: async (id: number, updates: UpdateOrder): Promise<Order | null> => {
    try {
      return await ordersApi.update(id, updates);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      const index = orders.findIndex(o => o.id === id);
      if (index !== -1) {
        orders[index] = { ...orders[index], ...updates };
        return orders[index];
      }
      return null;
    }
  },

  deleteOrder: async (id: number): Promise<void> => {
    try {
      await ordersApi.delete(id);
    } catch (error) {
      console.warn('API not available, using mock data:', error);
      orders = orders.filter(o => o.id !== id);
    }
  },

  // Contact Info - with API integration and fallback
  getContactInfo: async (): Promise<ContactInfo | null> => {
    try {
      return await contactApi.get();
    } catch (error) {
      console.warn('API not available, using default contact info:', error);
      return {
        id: 1,
        address: 'Default Address',
        phone: '+62 123 456 789',
        email: '<EMAIL>',
        map_embed: null
      };
    }
  },

  updateContactInfo: async (updates: UpdateContactInfo): Promise<ContactInfo | null> => {
    try {
      return await contactApi.update(updates);
    } catch (error) {
      console.warn('API not available, contact info update failed:', error);
      return null;
    }
  },
};
