
export interface Category {
  id: number;
  name: string;
  created_at: string;
}

export interface Book {
  id: number;
  category_id: number;
  title: string;
  author: string;
  description: string;
  stock: number;
  price_per_day: number;
  image?: string;
  created_at: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'user';
  created_at: string;
}

export interface Order {
  id: number;
  user_id: number;
  status: 'requested' | 'approved' | 'rejected';
  order_date: string;
  return_date: string;
  total_price: number;
  created_at: string;
}

export interface OrderItem {
  id: number;
  order_id: number;
  book_id: number;
  qty_days: number;
  subtotal: number;
}
