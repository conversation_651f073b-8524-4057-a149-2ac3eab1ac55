
import { AdminLayout } from "@/components/AdminLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Database, Server } from "lucide-react";

const AdminSettings = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">System configuration and settings</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                General Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="site_name">Site Name</Label>
                <Input id="site_name" defaultValue="Book Rental Service" />
              </div>
              <div>
                <Label htmlFor="site_description">Site Description</Label>
                <Textarea 
                  id="site_description" 
                  defaultValue="Professional book rental service for all your reading needs"
                />
              </div>
              <div>
                <Label htmlFor="contact_email">Contact Email</Label>
                <Input id="contact_email" type="email" defaultValue="<EMAIL>" />
              </div>
              <Button>Save General Settings</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Database Name</Label>
                <p className="text-sm text-muted-foreground font-mono">lsp_jwp</p>
              </div>
              <div className="space-y-2">
                <Label>Tables</Label>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>• books - Book inventory</p>
                  <p>• categories - Book categories</p>
                  <p>• users - System users</p>
                  <p>• orders - Rental orders</p>
                  <p>• order_items - Order line items</p>
                  <p>• contact_info - Contact information</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-green-800">API Status</p>
                  <p className="text-lg font-bold text-green-600">Online</p>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">Database</p>
                  <p className="text-lg font-bold text-blue-600">Connected</p>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg">
                  <p className="text-sm font-medium text-purple-800">Total Records</p>
                  <p className="text-lg font-bold text-purple-600">12</p>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <p className="text-sm font-medium text-orange-800">Version</p>
                  <p className="text-lg font-bold text-orange-600">1.0.0</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                Export Database Backup
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Clear Application Cache
              </Button>
              <Button variant="outline" className="w-full justify-start">
                View System Logs
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                Reset All Data
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
