// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vassycnriuoxylychflm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZhc3N5Y25yaXVveHlseWNoZmxtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MjAzOTcsImV4cCI6MjA2NTI5NjM5N30.uIxOeWoDn1YUUDD-xeC2s5kgIAW4FeY5_BWfvJAlc_s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);