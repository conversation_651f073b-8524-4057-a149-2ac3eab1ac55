# LSP JWP - Book Rental Management System

## Project Overview

This is a book rental management system built for LSP JWP. It provides an admin interface to manage books, categories, users, and rental orders.

## Features

- **Book Management**: Add, edit, delete, and view books
- **Category Management**: Organize books by categories
- **User Management**: Manage user accounts and roles
- **Order Management**: Track rental orders and their status
- **Admin Dashboard**: Overview of system statistics

## Technologies Used

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Framework**: shadcn/ui with Tailwind CSS
- **Database**: MySQL (lsp_jwp database)
- **State Management**: React Query + Local State
- **Routing**: React Router DOM

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- MySQL server
- phpMyAdmin (optional, for database management)

### Installation

1. Clone the repository:
```sh
git clone <repository-url>
cd lsp_project
```

2. Set up the database:
   - Make sure MySQL is installed and running
   - Import the `database/lsp_jwp.sql` file into your MySQL server:
   ```sh
   mysql -u root -p < database/lsp_jwp.sql
   ```
   - Or use phpMyAdmin to import the SQL file

3. Set up the backend API server:
```sh
cd server
npm install
cp .env.example .env
# Edit .env file with your database credentials
npm run dev
```

4. Set up the frontend:
```sh
# In a new terminal, from the project root
npm install
cp .env.example .env
# Edit .env file if needed
npm run dev
```

The application will be available at:
- Frontend: `http://localhost:8080`
- Backend API: `http://localhost:3001`

## Database Schema

The system uses the following main tables:
- `users` - User accounts and authentication
- `categories` - Book categories
- `books` - Book inventory
- `orders` - Rental orders
- `order_items` - Individual items in orders
- `contact_info` - Contact information settings

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── lib/           # Utilities and configurations
├── hooks/         # Custom React hooks
├── types/         # TypeScript type definitions
└── integrations/  # Database and API integrations
```
