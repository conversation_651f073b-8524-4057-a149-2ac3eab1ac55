// Database configuration for MySQL connection
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'lsp_jwp',
  port: parseInt(process.env.DB_PORT || '3306'),
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// API base URL for backend communication
export const apiConfig = {
  baseURL: process.env.API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000
};
